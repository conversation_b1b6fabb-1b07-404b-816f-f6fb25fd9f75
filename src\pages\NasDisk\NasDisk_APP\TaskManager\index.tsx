import { CapsuleTabs, Popover, Toast } from "antd-mobile";
import DownloadTask, { DownloadTaskRef } from "./DownloadTask";
import UploadBaiDu, { UploadBaiDuRef } from "./UploadBaiDu";
import { useHistory, useLocation } from "react-router-dom";
import { useState, useRef, useEffect } from "react";
import styles from "./index.module.scss";
import close from "@/Resources/camMgmtImg/close.png";
import closeDark from "@/Resources/camMgmtImg/close-dark.png";
import del from "@/Resources/camMgmtImg/delete.png";
import NavigatorBar from "@/components/NavBar";
import { PreloadImage } from "@/components/Image";
import { useTheme } from "@/utils/themeDetector";
import more from "@/Resources/camMgmtImg/more.png";
import more_dark from "@/Resources/camMgmtImg/more-dark.png";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";
import outlineIcon from "@/Resources/camMgmtImg/outline.png";
import outlineDarkIcon from "@/Resources/camMgmtImg/outline-dark.png";
const actions = [
  { key: "multiSelect", text: "选择" },
  { key: "mine", text: "我的" },
];


const TaskManager = () => {
  const location = useLocation();
  const state = location.state as { selectedIds: number[] } | undefined;
  const selectedIds = state?.selectedIds;
  console.log("selectedIds: ", selectedIds);
  const [isSearch, setIsSearch] = useState(false);
  const [isMultiSelect, setIsMultiSelect] = useState(false);
  const [activeTab, setActiveTab] = useState("management");

  // 处理tab切换
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    // 切换tab时退出多选模式
    if (isMultiSelect) {
      setIsMultiSelect(false);
      setSelectedTasksCount(0);
    }
  };
  const { isDarkMode } = useTheme();
  // 删除任务相关状态
  const [selectedTasksCount, setSelectedTasksCount] = useState(0);
  const [totalTasksCount, setTotalTasksCount] = useState(0);
  const downloadTaskRef = useRef<DownloadTaskRef>(null);
  const uploadBaiDuRef = useRef<UploadBaiDuRef>(null);

  const handleClick = (node: any) => {
    console.log("node: ", node);
    if (node.key === "mine") {
      history.push("/baiduNetdisk_app/members");
      setIsSearch(!isSearch);
      if (isMultiSelect) setIsMultiSelect(false);
    } else if (node.key === "multiSelect") {
      setIsMultiSelect(!isMultiSelect);
      if (isSearch) setIsSearch(false);
    } else if (node.key === "settings") {
      history.push("/baiduNetdisk_app/TaskManager/settings");
    }
  };
  const chooseAll = () => {
    // 根据当前活跃的tab调用相应组件的全选功能
    if (activeTab === "management" && downloadTaskRef.current) {
      downloadTaskRef.current.selectAllTasks?.(true);
    } else if (activeTab === "upload" && uploadBaiDuRef.current) {
      uploadBaiDuRef.current.selectAllTasks?.(true);
    }
  };
  const handleEnterMultiSelect = () => {
    setIsMultiSelect(true);
    if (isSearch) setIsSearch(false);
  };

  const handleExitMultiSelect = () => {
    setIsMultiSelect(false);
    setSelectedTasksCount(0);
  };

  // 处理任务选择数量变化
  const handleSelectedTasksChange = (count: number) => {
    setSelectedTasksCount(count);
  };

  // 处理任务总数变化
  const handleTotalTasksChange = (count: number) => {
    setTotalTasksCount(count);
  };

  // 处理删除任务按钮点击
  const handleDeleteClick = async () => {
    if (selectedTasksCount === 0) {
      Toast.show({
        content: '请至少选择一个任务',
      });
      return;
    }

    try {
      // 根据当前活跃的tab调用相应组件的删除功能
      if (activeTab === "management" && downloadTaskRef.current) {
        await downloadTaskRef.current.deleteSelectedTasks();
      } else if (activeTab === "upload" && uploadBaiDuRef.current) {
        await uploadBaiDuRef.current.deleteSelectedTasks();
      }

      // 删除成功后的退出多选模式逻辑已移至子组件处理
    } catch (error) {
      console.error('删除任务失败:', error);
      // Toast 错误信息已经在各自组件中处理了
    }
  };

  const right = (
    <div style={{ fontSize: 24 }}>
      <Popover.Menu
        actions={actions}
        placement="bottom-start"
        onAction={(node) => handleClick(node)}
        trigger="click"
      >
        <PreloadImage src={isDarkMode ? more_dark : more} style={{ width: '40px', height: '40px' }} alt="more" />
      </Popover.Menu>
    </div>
  );
    const rightTwo = (
    <div style={{ fontSize: 24 }}>
      <div onClick={chooseAll} style={{ cursor: 'pointer' }}>
        <PreloadImage src={isDarkMode ? outlineDarkIcon : outlineIcon} style={{ width: '40px', height: '40px' }} alt="select-all" />
      </div>
    </div>
  );

  const history = useHistory();

  const handleBack = () => {
    if (isMultiSelect) {
      // 如果在编辑态，退出编辑态
      setIsMultiSelect(false);
      setSelectedTasksCount(0);
    } else {
      // 否则返回上一页
      history.goBack();
    }
  };
  // 页面曝光埋点
  useEffect(() => {
    if (activeTab === 'management') {
      window.onetrack?.('track', 'nasDisk_DownloadTask_expose',
        '下载至存储'
      );
    } else {
      window.onetrack?.('track', 'nasDisk_UploadBaiDu_expose',
        '上传至网盘'
      );
    }
  }, [activeTab]);
  return (
    <div className={styles.taskManagerContainer}>
      <div className={styles.fixedHeader}>
        <NavigatorBar
          onBack={handleBack}
          right={isMultiSelect ? rightTwo : right}
          backIcon={isMultiSelect ? (isDarkMode ? closeDark : close) : (isDarkMode ? arrowLeftDark : arrowLeft)}
        />
        <div className={styles.titleContainer}>
          <div className={styles.title}>{isMultiSelect ? ' 删除任务' : '任务管理'}</div>
        </div>
        <div className={styles.taskTab}>
          <CapsuleTabs onChange={handleTabChange}>
            <CapsuleTabs.Tab title="下载至储存" key="management" />
            <CapsuleTabs.Tab
              title="上传至网盘"
              key="upload"
            />
          </CapsuleTabs>
        </div>
      </div>

      <div className={styles.scrollableContent}>
        {activeTab === "management" && (
          <DownloadTask
            ref={downloadTaskRef}
            isMultiSelect={isMultiSelect}
            onEnterMultiSelect={handleEnterMultiSelect}
            onSelectedTasksChange={handleSelectedTasksChange}
            onTotalTasksChange={handleTotalTasksChange}
            onExitMultiSelect={handleExitMultiSelect}
          />
        )}
        {activeTab === "upload" && (
          <UploadBaiDu
            ref={uploadBaiDuRef}
            isMultiSelect={isMultiSelect}
            onEnterMultiSelect={handleEnterMultiSelect}
            onSelectedTasksChange={handleSelectedTasksChange}
            onTotalTasksChange={handleTotalTasksChange}
            onExitMultiSelect={handleExitMultiSelect}
          />
        )}
      </div>

      {/* 多选模式下的操作按钮 */}
      {isMultiSelect && (
        <div className={styles.multiSelectActions}>
          <div className={styles.content} onClick={handleDeleteClick}>
            <PreloadImage src={del} alt="del" style={{ width: 30, height: 30 }} />
            <span className={styles.delName}>删除</span>
          </div>
        </div>
      )}


    </div>
  );
};

export default TaskManager;
